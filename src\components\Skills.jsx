import React from 'react';
import { Code2, Palette, Database, Globe, Smartphone, Zap } from 'lucide-react';

const Skills = () => {
  const skillCategories = [
    {
      icon: <Code2 className="w-8 h-8 text-primary-600" />,
      title: "Frontend Development",
      skills: [
        { name: "HTML5", level: 90 },
        { name: "CSS3", level: 85 },
        { name: "JavaScript", level: 80 },
        { name: "React", level: 75 },
      ]
    },
    {
      icon: <Palette className="w-8 h-8 text-primary-600" />,
      title: "Styling & Design",
      skills: [
        { name: "Tailwind CSS", level: 85 },
        { name: "SCSS/Sass", level: 80 },
        { name: "Responsive Design", level: 90 },
        { name: "UI/UX Principles", level: 75 },
      ]
    },
    {
      icon: <Database className="w-8 h-8 text-primary-600" />,
      title: "State Management",
      skills: [
        { name: "Redux", level: 65 },
        { name: "Context API", level: 80 },
        { name: "<PERSON><PERSON> Hooks", level: 85 },
        { name: "Props Management", level: 90 },
      ]
    },
    {
      icon: <Globe className="w-8 h-8 text-primary-600" />,
      title: "Tools & Technologies",
      skills: [
        { name: "Git & GitHub", level: 85 },
        { name: "Vite", level: 80 },
        { name: "NPM/Yarn", level: 75 },
        { name: "VS Code", level: 90 },
      ]
    }
  ];

  const additionalSkills = [
    { icon: <Smartphone className="w-6 h-6" />, name: "Responsive Design" },
    { icon: <Zap className="w-6 h-6" />, name: "Performance Optimization" },
    { icon: <Code2 className="w-6 h-6" />, name: "Clean Code Practices" },
    { icon: <Palette className="w-6 h-6" />, name: "Cross-browser Compatibility" },
  ];

  return (
    <section id="skills" className="section-padding bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Skills & Expertise
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Here are the technologies and tools I work with to create amazing web experiences.
          </p>
        </div>

        {/* Main Skills Grid */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {skillCategories.map((category, index) => (
            <div key={index} className="card">
              <div className="flex items-center mb-6">
                {category.icon}
                <h3 className="text-xl font-semibold text-gray-900 ml-3">
                  {category.title}
                </h3>
              </div>
              
              <div className="space-y-4">
                {category.skills.map((skill, skillIndex) => (
                  <div key={skillIndex}>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-700 font-medium">{skill.name}</span>
                      <span className="text-primary-600 font-semibold">{skill.level}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-1000 ease-out"
                        style={{ width: `${skill.level}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Additional Skills */}
        <div className="text-center">
          <h3 className="text-2xl font-semibold text-gray-900 mb-8">
            Additional Expertise
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {additionalSkills.map((skill, index) => (
              <div
                key={index}
                className="flex flex-col items-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300"
              >
                <div className="p-3 bg-primary-100 rounded-full text-primary-600 mb-3">
                  {skill.icon}
                </div>
                <span className="text-gray-700 font-medium text-center text-sm">
                  {skill.name}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Learning Journey */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Continuous Learning</h3>
            <p className="text-primary-100 mb-6 max-w-2xl mx-auto">
              I believe in staying updated with the latest technologies and best practices. 
              Currently exploring advanced React patterns, TypeScript, and modern CSS techniques.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              {['TypeScript', 'Next.js', 'Node.js', 'GraphQL'].map((tech) => (
                <span
                  key={tech}
                  className="px-4 py-2 bg-white/20 rounded-full text-sm font-medium"
                >
                  {tech}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
