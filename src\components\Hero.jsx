import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>ith<PERSON>, Linkedin, <PERSON> } from 'lucide-react';
import { Link } from 'react-router-dom';

const Hero = () => {
  const scrollToAbout = () => {
    const aboutSection = document.getElementById('about');
    if (aboutSection) {
      aboutSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-900 via-blue-800 to-purple-800 text-white relative overflow-hidden">
      {/* Background Animation */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-white rounded-full mix-blend-multiply filter blur-xl animate-float"></div>
        <div className="absolute top-1/3 right-1/4 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-float" style={{ animationDelay: '4s' }}></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <div className="fade-in-up">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
            Hi, I'm{' '}
            <span className="text-gradient">
              Dharmendra
            </span>
          </h1>
          <h2 className="text-2xl md:text-3xl font-light mb-8 text-blue-100">
            Frontend Developer & UI/UX Enthusiast
          </h2>
          <p className="text-lg md:text-xl text-blue-200 mb-12 max-w-3xl mx-auto leading-relaxed">
            I create beautiful, responsive web applications using modern technologies like React,
            JavaScript, and Tailwind CSS. Passionate about clean code and exceptional user experiences.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16 slide-in-left" style={{animationDelay: '0.3s'}}>
            <Link
              to="/projects"
              className="bg-white text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 shadow-lg hover-glow"
            >
              View My Work
            </Link>
            <Link
              to="/contact"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-900 transition-all duration-300 transform hover:scale-105"
            >
              Get In Touch
            </Link>
          </div>

          {/* Social Links */}
          <div className="flex justify-center space-x-6 mb-16 slide-in-right" style={{animationDelay: '0.6s'}}>
            <a
              href="https://github.com/dharam011"
              target="_blank"
              rel="noopener noreferrer"
              className="p-3 glass rounded-full hover:bg-white/20 transition-all duration-300 transform hover:scale-110 hover-glow"
            >
              <Github size={24} />
            </a>
            <a
              href="https://www.linkedin.com/in/dharmendra-kushwaha-ab3b0021b"
              target="_blank"
              rel="noopener noreferrer"
              className="p-3 glass rounded-full hover:bg-white/20 transition-all duration-300 transform hover:scale-110 hover-glow"
            >
              <Linkedin size={24} />
            </a>
            <a
              href="mailto:<EMAIL>"
              className="p-3 glass rounded-full hover:bg-white/20 transition-all duration-300 transform hover:scale-110 hover-glow"
            >
              <Mail size={24} />
            </a>
          </div>

          {/* Scroll Indicator */}
          <button
            onClick={scrollToAbout}
            className="animate-bounce-slow p-2 rounded-full glass hover:bg-white/20 transition-all duration-300 hover-lift fade-in-up"
            style={{animationDelay: '0.9s'}}
          >
            <ArrowDown size={24} />
          </button>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 w-full h-24 bg-gradient-to-t from-white to-transparent"></div>
    </section>
  );
};

export default Hero;
