import React from 'react';
import { Code, Palette, Zap, Users } from 'lucide-react';

const About = () => {
  const features = [
    {
      icon: <Code className="w-8 h-8 text-primary-600" />,
      title: "Clean Code",
      description: "I write maintainable, scalable code following best practices and modern standards."
    },
    {
      icon: <Palette className="w-8 h-8 text-primary-600" />,
      title: "UI/UX Design",
      description: "Creating beautiful, intuitive interfaces that provide excellent user experiences."
    },
    {
      icon: <Zap className="w-8 h-8 text-primary-600" />,
      title: "Performance",
      description: "Optimizing applications for speed and efficiency across all devices and browsers."
    },
    {
      icon: <Users className="w-8 h-8 text-primary-600" />,
      title: "Collaboration",
      description: "Working effectively in teams and communicating technical concepts clearly."
    }
  ];

  return (
    <section id="about" className="section-padding bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            About Me
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            I'm a passionate frontend developer with a keen eye for design and a love for creating 
            exceptional digital experiences.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Text Content */}
          <div className="space-y-6">
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">
              My Journey
            </h3>
            <p className="text-gray-600 leading-relaxed">
              I started my journey in web development with a curiosity about how websites work. 
              Over time, I've developed expertise in modern frontend technologies including React, 
              JavaScript, HTML5, CSS3, and Tailwind CSS.
            </p>
            <p className="text-gray-600 leading-relaxed">
              I believe in continuous learning and staying updated with the latest trends and 
              technologies in web development. My goal is to create applications that not only 
              look great but also provide seamless user experiences.
            </p>
            <p className="text-gray-600 leading-relaxed">
              When I'm not coding, you can find me exploring new design trends, contributing to 
              open-source projects, or learning about emerging technologies in the web development space.
            </p>

            {/* Skills Summary */}
            <div className="pt-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Core Technologies</h4>
              <div className="flex flex-wrap gap-3">
                {['HTML5', 'CSS3', 'JavaScript', 'React', 'Tailwind CSS', 'SCSS', 'Redux', 'Git'].map((skill) => (
                  <span
                    key={skill}
                    className="px-4 py-2 bg-primary-100 text-primary-800 rounded-full text-sm font-medium"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Features Grid */}
          <div className="grid sm:grid-cols-2 gap-6">
            {features.map((feature, index) => (
              <div
                key={index}
                className="card hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className="mb-4">
                  {feature.icon}
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h4>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Stats Section */}
        <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-3xl font-bold text-primary-600 mb-2">3+</div>
            <div className="text-gray-600">Projects Completed</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary-600 mb-2">2+</div>
            <div className="text-gray-600">Years Learning</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary-600 mb-2">8+</div>
            <div className="text-gray-600">Technologies</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary-600 mb-2">100%</div>
            <div className="text-gray-600">Dedication</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
